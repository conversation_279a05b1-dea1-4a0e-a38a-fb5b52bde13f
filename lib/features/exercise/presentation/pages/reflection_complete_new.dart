import 'dart:math';
import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/data_cache.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/reflection_request.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/reflection_data/reflection_data_cubit.dart';
import 'package:gotcha_mfg_app/features/favourites/data/models/favourite_request_model.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/pages/notification_allow_page.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import 'package:video_player/video_player.dart';
import 'package:volume_controller/volume_controller.dart';

import '../../../../config/theme/app_assets.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/popup.dart';
import '../../../notification/presentation/blocs/notification/notification_cubit.dart';
import '../../domain/usecases/post_exercise_use_case.dart';
import '../blocs/exercise/exercise_cubit.dart';

@RoutePage()
class ReflectionCompleteNewPage extends StatefulWidget {
  final List<Answer> answers;
  final int totalBars;
  final String? refId;
  final bool feedback;
  final bool isVillage;
  final bool notification;

  const ReflectionCompleteNewPage({
    super.key,
    required this.answers,
    required this.totalBars,
    required this.refId,
    required this.feedback,
    required this.isVillage,
    required this.notification,
  });

  @override
  State<ReflectionCompleteNewPage> createState() =>
      _ReflectionCompleteNewPageState();
}

class _ReflectionCompleteNewPageState extends State<ReflectionCompleteNewPage>
    with WidgetsBindingObserver {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  bool _isVideoComplete = false;
  var req = FavouriteRequest(id: '', isExercise: false);
  String? message;
  bool isShow = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setSystemUIOverlayStyle();
    sl<MixpanelService>().trackScreenView(
      'Reflection Complete Page',
      properties: {'Code': 'screen_view.reflection_complete_page'},
    );
    context.read<ExerciseCubit>().postExerciseResponse(
          PostExerciseDetailParams(
            widget.refId ?? "",
            ans: widget.answers,
          ),
          req,
          false,
        );
    // mixpanel
    sl<MixpanelService>().trackSubmissionWithOptions(
      'Reflection Complete Page',
      answers: widget.answers,
    );
    _initializeVideoController();
    message = getMessage() ?? 'Well done!';
    _initVolume();
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoPositionListener);
    _controller?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    _setSystemUIOverlayStyle();
  }

  void _setSystemUIOverlayStyle() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: AppColors.grey,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));
  }

  String? getVideo() {
    var datacache = DataCache();
    var wellDoneUrls = datacache.getData()?.data?.miscellaneous?.wellDoneUrls;
    if (wellDoneUrls == null || wellDoneUrls.isEmpty) return null;
    var random = Random();
    return wellDoneUrls[random.nextInt(wellDoneUrls.length)];
  }

  String? getMessage() {
    var datacache = DataCache();
    var messages = datacache.getData()?.data?.miscellaneous?.messages;
    if (messages == null || messages.isEmpty) return null;
    var random = Random();
    return messages[random.nextInt(messages.length)];
  }

  Future<void> _initializeVideoController() async {
    String? videoUrl = getVideo();
    if (videoUrl == null) {
      setState(() {
        _hasError = true;
      });
      return;
    }
    _controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));

    try {
      await _controller!.initialize();
      _controller!.setLooping(false);
      _controller!.addListener(_videoPositionListener);
      await _controller!.play();
      // await _controller!.setVolume(0.5);
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
      });
    }
  }

  void _videoPositionListener() {
    if (_controller != null &&
        _controller!.value.position >= _controller!.value.duration) {
      setState(() {
        _isVideoComplete = true;
      });
    }
  }

  Future<void> _checkAndSetVolume() async {
    try {
      final volume = await VolumeController().getVolume();
      if (volume < 0.5) {
        VolumeController().setVolume(0.5);
      }
    } catch (e) {
      info('Error setting volume: $e');
    }
  }

  void _initVolume() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      unawaited(_checkAndSetVolume());
    });
  }

  @override
  void deactivate() {
    _controller?.pause();
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ReflectionDataCubit>();
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.grey,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: BlocConsumer<ExerciseCubit, ExerciseState>(
          listener: (context, state) {
        if (state is ExerciseError) {
          SnackBarService.error(
            context: context,
            message: state.error,
          );
        }
        if (state is ExerciseLoaded) {
          cubit.clearAnswers();
        }
        if (state is ExerciseUpLoaded) {
          setState(() {
            isShow = state.data?.data?.canShowWellDone ?? false;
          });
          if (state.data?.data?.notification != null) {
            CustomCupertinoAlertDialog.showAlertPopup(
              okButtonText: 'OK',
              context,
              title: state.data?.data?.notification?.title ?? 'N/A',
              content: state.data?.data?.notification?.body ?? 'N/A',
              onOk: () {},
            );
          }
          context.read<NotificationCubit>().getNotificationCount();
        }
      }, builder: (context, state) {
        if (state is ExerciseLoading) {
          return const LoadingWidget(color: Colors.white);
        }
        if (state is ExerciseError) {
          return RetryWidget(
            onRetry: () {
              context.read<ExerciseCubit>().postExerciseResponse(
                    PostExerciseDetailParams(
                      widget.refId ?? "",
                      ans: widget.answers,
                    ),
                    req,
                    false,
                  );
            },
            color: Colors.white,
          );
        }
        return Scaffold(
          resizeToAvoidBottomInset: true,
          backgroundColor: Colors.white,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(0),
            child: AppBar(
              elevation: 0,
              backgroundColor: Colors.white,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: AppColors.grey,
                systemNavigationBarIconBrightness: Brightness.dark,
              ),
            ),
          ),
          body: Padding(
            padding: EdgeInsets.only(
              top: isIos ? 4 : 8,
              left: 8,
              right: 8,
            ),
            child: Column(
              children: [
                AppHeader(
                  title: 'Exercise complete!',
                  currentStep: (widget.totalBars) + 1,
                  totalSteps: (widget.totalBars) + 1,
                ),
                Expanded(
                  child: Container(
                    color: AppColors.navy,
                    child: Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        color: AppColors.grey,
                      ),
                      child: Stack(
                        children: [
                          Positioned.fill(
                            bottom: 56,
                            child: Center(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 0,
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    if (isShow)
                                      Text(
                                        message ?? "Well done!",
                                        textAlign: TextAlign.center,
                                        style: textTheme.gothamBold
                                            .copyWith(fontSize: 26),
                                      ),
                                    if (isShow)
                                      (message?.length ?? 0) > 22
                                          ? const Gap(24)
                                          : const Gap(32),
                                    Container(
                                      width: size.width,
                                      height: size.width > 600
                                          ? size.height / 1.5
                                          : size.height / (isShow ? 2 : 1.8),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(30),
                                        child: Stack(
                                          fit: StackFit.expand,
                                          children: [
                                            // Background image to show after video ends
                                            AnimatedOpacity(
                                              opacity:
                                                  _isVideoComplete ? 1.0 : 0.0,
                                              duration: const Duration(
                                                  milliseconds: 800),
                                              child: Container(
                                                color: AppColors.midBlue,
                                                width: size.width,
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Image.asset(
                                                      AppAssets.fliptextlogo,
                                                      fit: BoxFit.cover,
                                                      width: size.width / 4,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            // Video player with fade out animation
                                            AnimatedOpacity(
                                              opacity:
                                                  _isVideoComplete ? 0.0 : 1.0,
                                              duration: const Duration(
                                                  milliseconds: 800),
                                              child: _buildVideoPlayer(),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const Gap(24),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 32,
              vertical: 24,
            ),
            child: SizedBox(
              width: size.width,
              child: PrimaryButton(
                  text: 'Next',
                  isEnabled: true,
                  onPressed: () {
                    //edit in progress
                    if (widget.notification == true) {
                      _controller?.pause();
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const NotificationAllowPage(),
                        ),
                      );
                      // mixpanel
                      sl<MixpanelService>()
                          .trackButtonClick('Next', properties: {
                        'Page': 'Reflection Complete Page',
                        'Code': 'click.reflection_complete_page.next'
                      });
                      return;
                    }
                    if (widget.isVillage) {
                      CustomCupertinoAlertDialog.yesOrNoPopup(
                        context,
                        title: "Check-in",
                        content: "Do you want to check-in with your village?",
                        onYes: () {
                          context.router.replaceAll(
                            [HomeRoute(index: 2), const VillageHomeRoute()],
                            updateExistingRoutes: false,
                          );
                          //mixpanel
                          sl<MixpanelService>().trackButtonClick(
                            'Yes - Go to Village',
                            properties: {
                              'Page': 'Exercise Feedback Page',
                              'Code': 'click.exercise_feedback_page.alert_yes'
                            },
                          );
                          return;
                        },
                        onNo: () {
                          Navigator.pop(context);
                          context.router.replaceAll(
                            [HomeRoute(index: 0)],
                            updateExistingRoutes: false,
                          );
                          //mixpanel
                          sl<MixpanelService>().trackButtonClick(
                            'No - Go to Home',
                            properties: {
                              'Page': 'Exercise Feedback Page',
                              'Code': 'click.exercise_feedback_page.alert_no'
                            },
                          );
                          return;
                        },
                      );
                    } else {
                      _controller?.pause();

                      context.read<ReflectionDataCubit>().clearAnswers();
                      if (widget.feedback) {
                        context.pushRoute(
                            ExerciseFeedbackRoute(feedback: widget.feedback));
                        return;
                      } else {
                        context.router.replaceAll(
                          [HomeRoute(index: 0)],
                          updateExistingRoutes: false,
                        );
                        return;
                      }
                    }
                  }),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildVideoPlayer() {
    if (_hasError) {
      return Container(
        color: AppColors.grey,
        child: Center(
          child: Text(
            "Error loading video.",
            style: Theme.of(context).textTheme.bodyEmphasis,
          ),
        ),
      );
    } else if (_isInitialized && _controller != null) {
      return FittedBox(
        fit: BoxFit.cover,
        clipBehavior: Clip.hardEdge,
        child: SizedBox(
          width: _controller!.value.size.width,
          height: _controller!.value.size.height,
          child: VideoPlayer(_controller!),
        ),
      );
    } else {
      return Container(
        color: AppColors.grey,
        child: const Center(
          child: Loader(),
        ),
      );
    }
  }
}
